import {
	ActionRowBuilder,
	ButtonBuilder,
	ButtonStyle,
	ChannelType,
	ContainerBuilder,
	Events,
	MessageFlags,
	PermissionFlagsBits,
	SectionBuilder,
	SeparatorBuilder,
	TextDisplayBuilder,
	ThumbnailBuilder,
} from "discord.js";
import type { BotEventHandler } from "../interfaces/botTypes.js";
import { getHubChannel } from "../utils/guildManager.js";
import { emojis, getEmoji } from "../utils/emojis.js";
import { getMusicManager } from "../utils/musicUtils.js";

// Handle music system voice state changes
async function handleMusicVoiceStateUpdate(oldState: any, newState: any) {
	try {
		const musicManager = getMusicManager();

		// Handle bot being moved/disconnected
		if (newState.member?.user.bot && newState.member.id === newState.client.user?.id) {
			const queue = musicManager.getQueue(newState.guild.id);

			if (!queue) return;

			// <PERSON><PERSON> was disconnected from voice channel
			if (oldState.channelId && !newState.channelId) {
				queue.destroy();
				return;
			}

			// Bot was moved to a different channel
			if (oldState.channelId && newState.channelId && oldState.channelId !== newState.channelId) {
				// Update the queue's voice channel reference
				if (newState.channel) {
					queue.voiceChannel = newState.channel as any;
				}
				return;
			}
		}

		// Handle users leaving/joining voice channels
		if (!newState.member?.user.bot) {
			const queue = musicManager.getQueue(newState.guild.id);
			if (!queue || !queue.voiceChannel) return;

			// Check if the bot is alone in the voice channel
			const botVoiceChannel = newState.guild.members.me?.voice.channel;
			if (!botVoiceChannel || botVoiceChannel.id !== queue.voiceChannel.id) return;

			// Count non-bot members in the voice channel
			const nonBotMembers = botVoiceChannel.members.filter((member: any) => !member.user.bot);

			// If bot is alone, start a timer to disconnect after 5 minutes
			if (nonBotMembers.size === 0) {
				// Set a timeout to disconnect if still alone
				setTimeout(
					() => {
						const currentQueue = musicManager.getQueue(newState.guild.id);
						if (!currentQueue) return;

						const currentBotChannel = newState.guild.members.me?.voice.channel;
						if (!currentBotChannel) return;

						const currentNonBotMembers = currentBotChannel.members.filter(
							(member: any) => !member.user.bot,
						);

						// If still alone, disconnect
						if (currentNonBotMembers.size === 0) {
							currentQueue.destroy();
						}
					},
					5 * 60 * 1000,
				); // 5 minutes
			}
		}
	} catch (error) {
		console.error("Error in music voice state update handler:", error);
	}
}

const voiceStateUpdateEvent: BotEventHandler<Events.VoiceStateUpdate> = {
	name: Events.VoiceStateUpdate,
	once: false,
	execute: async (oldState, newState) => {
		try {
			await handleMusicVoiceStateUpdate(oldState, newState);

			try {
				const member = newState.member;
				if (!member) return;

				const guild = newState.guild;
				const hubChannelId = await getHubChannel(guild.id);
				if (!hubChannelId) return;

				if (newState.channelId !== hubChannelId) return;
				if (oldState.channelId === hubChannelId) return;

				const botMember = guild.members.me;
				if (
					!botMember?.permissions.has([
						PermissionFlagsBits.ManageChannels,
						PermissionFlagsBits.MoveMembers,
						PermissionFlagsBits.ManageRoles,
					])
				)
					return;

				const personalVC = await guild.channels.create({
					name: `✪ ${member.user.displayName}'s`,
					type: ChannelType.GuildVoice,
					parent: newState.channel?.parent ?? undefined,
					permissionOverwrites: [
						{
							id: member.id,
							allow: [
								PermissionFlagsBits.Connect,
								PermissionFlagsBits.Speak,
								PermissionFlagsBits.ManageChannels,
								PermissionFlagsBits.PrioritySpeaker,
								PermissionFlagsBits.DeafenMembers,
								PermissionFlagsBits.MuteMembers,
								PermissionFlagsBits.MoveMembers,
							],
						},
						{
							id: guild.roles.everyone,
							deny: [PermissionFlagsBits.Connect],
						},
						{
							id: guild.members.me!.id,
							allow: [
								PermissionFlagsBits.Connect,
								PermissionFlagsBits.Speak,
								PermissionFlagsBits.ManageChannels,
								PermissionFlagsBits.PrioritySpeaker,
								PermissionFlagsBits.DeafenMembers,
								PermissionFlagsBits.MuteMembers,
								PermissionFlagsBits.MoveMembers,
							],
						},
					],
				});

				try {
					await member.voice.setChannel(personalVC);
				} catch (error) {
					console.error("Failed to move user to personal VC:", error);
					// Channel was created but user couldn't be moved
					// User can manually join the channel
				}

				const container = new ContainerBuilder()
					.addTextDisplayComponents(
						new TextDisplayBuilder().setContent(
							`-# You are the operator, @${member.user.username}`,
						),
					)
					.addSeparatorComponents(new SeparatorBuilder().setDivider(true))
					.addSectionComponents(
						new SectionBuilder()
							.addTextDisplayComponents(
								new TextDisplayBuilder().setContent(
									[
										`### ${getEmoji("lock_dotted")} Welcome to _superrr_ private!`,
										"This is your personal space.",
										"No one can join unless **you let them**.",
									].join("\n"),
								),
							)
							.setThumbnailAccessory(
								new ThumbnailBuilder().setURL(
									"https://media.discordapp.net/attachments/736571695170584576/1407074770922504332/kaeru_mic.png?ex=68a4c7ff&is=68a3767f&hm=b640ebe24d03cb43600e1122690190ccff64cd995632a534d7a0d6e098955229&=&width=614&height=610",
								),
							),
					);

				const text = new TextDisplayBuilder().setContent(
					`-# Feel free to unlock channel to others join`,
				);

				const buttonRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
					new ButtonBuilder()
						.setCustomId(`vc-lock-${personalVC.id}`)
						.setEmoji(emojis.lock_fill)
						.setLabel("Lock/Unlock")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder()
						.setCustomId(`vc-kick-${personalVC.id}`)
						.setEmoji(emojis.avatar)
						.setLabel("Kick Member")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder()
						.setCustomId(`vc-limit-${personalVC.id}`)
						.setEmoji(emojis.number_point)
						.setLabel("Set User Limit")
						.setStyle(ButtonStyle.Secondary),
				);

				await personalVC.send({
					components: [container, text, buttonRow],
					flags: MessageFlags.IsComponentsV2,
				});

				const deleteChecker = async () => {
					try {
						if (!personalVC.guild) return;
						if (personalVC.members.size === 0) {
							await personalVC.delete().catch(() => null);
						} else {
							setTimeout(deleteChecker, 5000);
						}
					} catch (err) {
						console.error("Delete checker error:", err);
					}
				};
				setTimeout(deleteChecker, 5000);
			} catch (err) {
				console.error("VoiceStateUpdate error:", err);
			}
		} catch (err) {
			console.error("VoiceStateUpdate error:", err);
		}
	},
};

export default voiceStateUpdateEvent;
