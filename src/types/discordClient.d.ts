import type { Collection } from "discord.js";
import type {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ommand,
	SelectMenuCommand,
	<PERSON><PERSON><PERSON>ommand,
	BotEventHandler,
} from "../interfaces/botTypes.js";

declare module "discord.js" {
	interface Client {
		commands: Collection<string, BotCommand>;
		buttons: Collection<string, ButtonCommand>;
		selectMenus: Collection<string, SelectMenuCommand<any>>;
		modals: Collection<string, ModalCommand>;
		events: Collection<string, BotEventHandler<keyof import("discord.js").ClientEvents>>;
		fileCache: Map<
			string,
			{
				text: string;
				ext: string;
				name: string;
				isViewable: boolean;
				collaborators: string[];
				owner: string;
				threadId?: string;
			}
		>;
	}
}
