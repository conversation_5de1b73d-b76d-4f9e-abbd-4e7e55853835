import { Guild, VoiceChannel, TextChannel, EmbedBuilder } from "discord.js";
import { Player } from "shoukaku";
import { MusicManager } from "./MusicManager.js";
import { Track } from "./Track.js";

export enum LoopMode {
	NONE = 0,
	TRACK = 1,
	QUEUE = 2,
}

export class MusicQueue {
	public manager: MusicManager;
	public guild: Guild;
	public voiceChannel: VoiceChannel;
	public textChannel: TextChannel;
	public player: Player | null = null;
	public tracks: Track[] = [];
	public currentTrack: Track | null = null;
	public previousTracks: Track[] = [];
	public volume: number = 100;
	public loop: LoopMode = LoopMode.NONE;
	public paused: boolean = false;

	constructor(
		manager: MusicManager,
		guild: Guild,
		voiceChannel: VoiceChannel,
		textChannel: TextChannel,
	) {
		this.manager = manager;
		this.guild = guild;
		this.voiceChannel = voiceChannel;
		this.textChannel = textChannel;
	}

	public async connect(): Promise<void> {
		if (this.player) return;

		this.player = await this.manager.createPlayer(this.guild.id, this.voiceChannel.id);
		this.setupPlayerEvents();
	}

	private setupPlayerEvents(): void {
		if (!this.player) return;

		this.player.on("start", () => {
			console.log(`Started playing in ${this.guild.name}`);
		});

		this.player.on("end", reason => {
			console.log(`Track ended in ${this.guild.name}: ${reason.reason}`);
			this.handleTrackEnd();
		});

		this.player.on("closed", reason => {
			console.log(`Player closed in ${this.guild.name}:`, reason);
			this.destroy();
		});

		this.player.on("exception", reason => {
			console.error(`Player exception in ${this.guild.name}:`, reason);
			this.handleTrackEnd();
		});
	}

	public async play(track?: Track): Promise<void> {
		if (!this.player) {
			await this.connect();
		}

		if (!this.player) {
			throw new Error("Failed to create player");
		}

		const trackToPlay = track || this.tracks.shift();
		if (!trackToPlay) {
			return;
		}

		this.currentTrack = trackToPlay;
		await this.player.playTrack({ track: { encoded: trackToPlay.encoded } });

		// Send now playing message
		await this.sendNowPlayingMessage();
	}

	public async skip(): Promise<boolean> {
		if (!this.player || !this.currentTrack) {
			return false;
		}

		await this.player.stopTrack();
		return true;
	}

	public async previous(): Promise<boolean> {
		if (this.previousTracks.length === 0) {
			return false;
		}

		const previousTrack = this.previousTracks.pop();
		if (!previousTrack) {
			return false;
		}

		// Add current track back to the beginning of queue
		if (this.currentTrack) {
			this.tracks.unshift(this.currentTrack);
		}

		await this.play(previousTrack);
		return true;
	}

	public async pause(): Promise<void> {
		if (!this.player) return;

		await this.player.setPaused(true);
		this.paused = true;
	}

	public async resume(): Promise<void> {
		if (!this.player) return;

		await this.player.setPaused(false);
		this.paused = false;
	}

	public async setVolume(volume: number): Promise<void> {
		if (!this.player) return;

		this.volume = Math.max(0, Math.min(100, volume));
		await this.player.setGlobalVolume(this.volume);
	}

	public async stop(): Promise<void> {
		if (!this.player) return;

		this.tracks = [];
		this.currentTrack = null;
		await this.player.stopTrack();
	}

	public addTrack(track: Track): void {
		this.tracks.push(track);
	}

	public addTracks(tracks: Track[]): void {
		this.tracks.push(...tracks);
	}

	public clearQueue(): void {
		this.tracks = [];
	}

	public shuffle(): void {
		for (let i = this.tracks.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[this.tracks[i], this.tracks[j]] = [this.tracks[j], this.tracks[i]];
		}
	}

	private async handleTrackEnd(): Promise<void> {
		if (!this.currentTrack) return;

		// Add to previous tracks
		this.previousTracks.push(this.currentTrack);
		if (this.previousTracks.length > 10) {
			this.previousTracks.shift();
		}

		// Handle loop modes
		if (this.loop === LoopMode.TRACK && this.currentTrack) {
			await this.play(this.currentTrack);
			return;
		}

		if (this.loop === LoopMode.QUEUE && this.currentTrack) {
			this.tracks.push(this.currentTrack);
		}

		// Play next track
		if (this.tracks.length > 0) {
			await this.play();
		} else {
			this.currentTrack = null;
			// Auto-disconnect after 5 minutes of inactivity
			setTimeout(
				() => {
					if (!this.currentTrack && this.tracks.length === 0) {
						this.destroy();
					}
				},
				5 * 60 * 1000,
			);
		}
	}

	private async sendNowPlayingMessage(): Promise<void> {
		if (!this.currentTrack) return;

		const embed = new EmbedBuilder()
			.setTitle("🎵 Now Playing")
			.setDescription(`**${this.currentTrack.title}**\nBy: ${this.currentTrack.author}`)
			.setThumbnail(this.currentTrack.thumbnail)
			.addFields(
				{ name: "Duration", value: this.formatDuration(this.currentTrack.duration), inline: true },
				{ name: "Requested by", value: `<@${this.currentTrack.requester}>`, inline: true },
				{ name: "Queue", value: `${this.tracks.length} tracks remaining`, inline: true },
			)
			.setColor(0x00ff00);

		await this.textChannel.send({ embeds: [embed] });
	}

	private formatDuration(ms: number): string {
		const seconds = Math.floor(ms / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);

		if (hours > 0) {
			return `${hours}:${(minutes % 60).toString().padStart(2, "0")}:${(seconds % 60).toString().padStart(2, "0")}`;
		}
		return `${minutes}:${(seconds % 60).toString().padStart(2, "0")}`;
	}

	public destroy(): void {
		if (this.player) {
			this.player.destroy();
			this.player = null;
		}
		// Don't call manager.deleteQueue here to avoid infinite recursion
		// The manager will handle queue deletion
	}
}
