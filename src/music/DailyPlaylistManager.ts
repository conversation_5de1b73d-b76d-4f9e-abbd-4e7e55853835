import { MusicManager } from "./MusicManager.js";
import { Track } from "./Track.js";

export enum PlaylistCategory {
	NEW_MIX = "new_mix",
	CHILL = "chill",
	ENERGETIC = "energetic",
}

interface DailyPlaylist {
	category: PlaylistCategory;
	tracks: Track[];
	lastUpdated: Date;
	guildId: string;
}

export class DailyPlaylistManager {
	private musicManager: MusicManager;
	private playlists: Map<string, Map<PlaylistCategory, DailyPlaylist>>;
	private refreshInterval: NodeJS.Timeout | null = null;

	// Predefined search queries for each category
	private readonly playlistQueries = {
		[PlaylistCategory.NEW_MIX]: [
			"new music 2024",
			"trending songs",
			"latest hits",
			"new releases",
			"popular music",
			"viral songs",
			"chart toppers",
			"fresh music",
		],
		[PlaylistCategory.CHILL]: [
			"chill music",
			"relaxing songs",
			"lofi hip hop",
			"ambient music",
			"calm music",
			"peaceful songs",
			"study music",
			"meditation music",
		],
		[PlaylistCategory.ENERGETIC]: [
			"energetic music",
			"workout songs",
			"upbeat music",
			"dance music",
			"pump up songs",
			"high energy",
			"motivational music",
			"party music",
		],
	};

	constructor(musicManager: MusicManager) {
		this.musicManager = musicManager;
		this.playlists = new Map();
		this.startWeeklyRefresh();
	}

	private startWeeklyRefresh(): void {
		// Refresh playlists every week (7 days)
		const weekInMs = 7 * 24 * 60 * 60 * 1000;

		this.refreshInterval = setInterval(() => {
			this.refreshAllPlaylists();
		}, weekInMs);

		// Also refresh on startup if playlists are old
		this.refreshAllPlaylists();
	}

	private async refreshAllPlaylists(): Promise<void> {
		console.log("Refreshing daily playlists...");

		for (const [guildId] of this.playlists) {
			for (const category of Object.values(PlaylistCategory)) {
				await this.refreshPlaylist(guildId, category);
			}
		}
	}

	public async getPlaylist(guildId: string, category: PlaylistCategory): Promise<Track[]> {
		if (!this.playlists.has(guildId)) {
			this.playlists.set(guildId, new Map());
		}

		const guildPlaylists = this.playlists.get(guildId)!;
		const playlist = guildPlaylists.get(category);

		// Check if playlist exists and is not older than a week
		const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

		if (!playlist || playlist.lastUpdated < oneWeekAgo) {
			await this.refreshPlaylist(guildId, category);
		}

		return guildPlaylists.get(category)?.tracks || [];
	}

	private async refreshPlaylist(guildId: string, category: PlaylistCategory): Promise<void> {
		try {
			const queries = this.playlistQueries[category];
			const allTracks: Track[] = [];

			// Search for tracks using multiple queries
			for (const query of queries.slice(0, 3)) {
				// Limit to 3 queries to avoid rate limits
				try {
					const tracks = await this.musicManager.searchTracks(query);
					// Take first 5 tracks from each search
					allTracks.push(...tracks.slice(0, 5));
				} catch (error) {
					console.error(`Failed to search for ${query}:`, error);
				}
			}

			// Remove duplicates and shuffle
			const uniqueTracks = this.removeDuplicates(allTracks);
			const shuffledTracks = this.shuffleArray(uniqueTracks);

			// Store the playlist
			if (!this.playlists.has(guildId)) {
				this.playlists.set(guildId, new Map());
			}

			const guildPlaylists = this.playlists.get(guildId)!;
			guildPlaylists.set(category, {
				category,
				tracks: shuffledTracks.slice(0, 20), // Limit to 20 tracks
				lastUpdated: new Date(),
				guildId,
			});

			console.log(
				`Refreshed ${category} playlist for guild ${guildId}: ${shuffledTracks.length} tracks`,
			);
		} catch (error) {
			console.error(`Failed to refresh ${category} playlist for guild ${guildId}:`, error);
		}
	}

	private removeDuplicates(tracks: Track[]): Track[] {
		const seen = new Set<string>();
		return tracks.filter(track => {
			const key = `${track.title}-${track.author}`.toLowerCase();
			if (seen.has(key)) {
				return false;
			}
			seen.add(key);
			return true;
		});
	}

	private shuffleArray<T>(array: T[]): T[] {
		const shuffled = [...array];
		for (let i = shuffled.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
		}
		return shuffled;
	}

	public getCategoryDisplayName(category: PlaylistCategory): string {
		switch (category) {
			case PlaylistCategory.NEW_MIX:
				return "🆕 New Mix";
			case PlaylistCategory.CHILL:
				return "😌 Chill";
			case PlaylistCategory.ENERGETIC:
				return "⚡ Energetic";
			default:
				return category;
		}
	}

	public getCategoryDescription(category: PlaylistCategory): string {
		switch (category) {
			case PlaylistCategory.NEW_MIX:
				return "Latest trending songs and new releases";
			case PlaylistCategory.CHILL:
				return "Relaxing and calm music for studying or unwinding";
			case PlaylistCategory.ENERGETIC:
				return "High-energy music perfect for workouts and motivation";
			default:
				return "A curated playlist";
		}
	}

	public getPlaylistInfo(
		guildId: string,
		category: PlaylistCategory,
	): { lastUpdated: Date | null; trackCount: number } {
		const guildPlaylists = this.playlists.get(guildId);
		const playlist = guildPlaylists?.get(category);

		return {
			lastUpdated: playlist?.lastUpdated || null,
			trackCount: playlist?.tracks.length || 0,
		};
	}

	public async forceRefreshPlaylist(guildId: string, category: PlaylistCategory): Promise<void> {
		await this.refreshPlaylist(guildId, category);
	}

	public destroy(): void {
		if (this.refreshInterval) {
			clearInterval(this.refreshInterval);
			this.refreshInterval = null;
		}
	}
}
