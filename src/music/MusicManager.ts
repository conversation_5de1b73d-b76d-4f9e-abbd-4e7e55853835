import { Client, Guild, VoiceChannel, TextChannel } from "discord.js";
import { <PERSON>houkaku, Connectors, Player, Node } from "shoukaku";
import { MusicQueue } from "./MusicQueue.js";
import { Track } from "./Track.js";
import { DailyPlaylistManager } from "./DailyPlaylistManager.js";

export class MusicManager {
	private client: Client;
	private shoukaku: Shoukaku;
	private queues: Map<string, MusicQueue>;
	public dailyPlaylistManager: DailyPlaylistManager;

	constructor(client: Client) {
		this.client = client;
		this.queues = new Map();

		// Initialize Shoukaku (Lavalink client)
		const nodes = this.getLavalinkNodes();
		this.shoukaku = new Shoukaku(new Connectors.DiscordJS(client), nodes, {
			moveOnDisconnect: false,
			resume: false,
			resumeTimeout: 30,
			reconnectTries: 5,
			restTimeout: 15000,
		});

		// Initialize Daily Playlist Manager
		this.dailyPlaylistManager = new DailyPlaylistManager(this);

		this.setupEvents();
	}

	private getLavalinkNodes() {
		// Use environment variables if available, otherwise fall back to defaults
		const lavalinkUrl = process.env.LAVALINK_URL || "localhost:2333";
		const lavalinkPassword = process.env.LAVALINK_PASSWORD || "youshallnotpass";
		const lavalinkSecure = process.env.LAVALINK_SECURE === "true";

		// Primary node (local or configured)
		const nodes = [
			{
				name: "main",
				url: lavalinkUrl,
				auth: lavalinkPassword,
				secure: lavalinkSecure,
			},
		];

		// Add fallback public nodes if using local setup
		if (lavalinkUrl === "localhost:2333") {
			nodes.push(
				{
					name: "backup1",
					url: "lava.luke.gg:443",
					auth: "discordbotlist.com",
					secure: true,
				},
				{
					name: "backup2",
					url: "pool-us.alfari.id:443",
					auth: "alfari",
					secure: true,
				},
				{
					name: "backup3",
					url: "lavalink.devxcode.in:443",
					auth: "DevamOP",
					secure: true,
				},
			);
		}

		return nodes;
	}

	private setupEvents(): void {
		this.shoukaku.on("ready", (name: string) => {
			console.log(`✅ Lavalink node ${name} is ready!`);
		});

		this.shoukaku.on("error", (name: string, error: Error) => {
			console.error(`❌ Lavalink node ${name} error:`, error);
		});

		this.shoukaku.on("close", (name: string, code: number, reason: string) => {
			console.log(`⚠️ Lavalink node ${name} closed with code ${code}: ${reason}`);
		});

		this.shoukaku.on("disconnect", (name: string, count: number) => {
			console.log(`⚠️ Lavalink node ${name} disconnected with ${count} players`);
		});

		console.log("🔄 Attempting to connect to Lavalink nodes...");
		console.log("📡 Main: lava.luke.gg:443");
		console.log("📡 Backup1: pool-us.alfari.id:443");
		console.log("📡 Backup2: lavalink.devxcode.in:443");
	}

	public async createQueue(
		guild: Guild,
		voiceChannel: VoiceChannel,
		textChannel: TextChannel,
	): Promise<MusicQueue> {
		if (this.queues.has(guild.id)) {
			return this.queues.get(guild.id)!;
		}

		const queue = new MusicQueue(this, guild, voiceChannel, textChannel);
		this.queues.set(guild.id, queue);
		return queue;
	}

	public getQueue(guildId: string): MusicQueue | undefined {
		return this.queues.get(guildId);
	}

	public deleteQueue(guildId: string): void {
		const queue = this.queues.get(guildId);
		if (queue) {
			queue.destroy();
			this.queues.delete(guildId);
		}
	}

	public async searchTracks(query: string): Promise<Track[]> {
		const node = this.shoukaku.getIdealNode();
		if (!node) {
			throw new Error("No available Lavalink nodes");
		}

		// Determine if it's a URL or search query
		const isUrl = /^https?:\/\//.test(query);
		const searchQuery = isUrl ? query : `ytsearch:${query}`;

		const result = await node.rest.resolve(searchQuery);

		if (!result || (result.loadType !== "search" && result.loadType !== "track")) {
			return [];
		}

		const tracks = result.data;
		if (!tracks || (Array.isArray(tracks) ? tracks.length === 0 : !tracks)) {
			return [];
		}

		const trackArray = Array.isArray(tracks) ? tracks : [tracks];
		return trackArray.map((track: any) => new Track(track, query));
	}

	public async createPlayer(guildId: string, voiceChannelId: string): Promise<Player> {
		const node = this.shoukaku.getIdealNode();
		if (!node) {
			throw new Error("No available Lavalink nodes");
		}

		const player = this.shoukaku.joinVoiceChannel({
			guildId,
			channelId: voiceChannelId,
			shardId: 0,
		});

		return player;
	}

	public get nodes(): Map<string, Node> {
		return this.shoukaku.nodes;
	}
}
