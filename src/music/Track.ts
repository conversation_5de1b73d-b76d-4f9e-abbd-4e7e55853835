export class Track {
	public encoded: string;
	public title: string;
	public author: string;
	public duration: number;
	public uri: string;
	public thumbnail: string;
	public requester: string;
	public originalQuery: string;

	constructor(trackData: any, originalQuery: string, requester?: string) {
		this.encoded = trackData.encoded || trackData.track;
		this.title = trackData.info?.title || "Unknown Title";
		this.author = trackData.info?.author || "Unknown Artist";
		this.duration = trackData.info?.length || 0;
		this.uri = trackData.info?.uri || "";
		this.thumbnail = this.extractThumbnail(trackData.info);
		this.requester = requester || "Unknown";
		this.originalQuery = originalQuery;
	}

	private extractThumbnail(info: any): string {
		if (info?.artworkUrl) {
			return info.artworkUrl;
		}

		// Extract YouTube thumbnail from URI
		if (info?.uri && info.uri.includes("youtube.com")) {
			const videoId = this.extractYouTubeVideoId(info.uri);
			if (videoId) {
				return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
			}
		}

		// Default thumbnail
		return "https://via.placeholder.com/480x360?text=Music";
	}

	private extractYouTubeVideoId(url: string): string | null {
		const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
		const match = url.match(regex);
		return match ? match[1] : null;
	}

	public getFormattedDuration(): string {
		const seconds = Math.floor(this.duration / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);

		if (hours > 0) {
			return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
		}
		return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
	}

	public isLiveStream(): boolean {
		return this.duration === 0;
	}

	public getDisplayTitle(): string {
		return this.title.length > 50 ? `${this.title.substring(0, 47)}...` : this.title;
	}

	public getDisplayAuthor(): string {
		return this.author.length > 30 ? `${this.author.substring(0, 27)}...` : this.author;
	}
}
