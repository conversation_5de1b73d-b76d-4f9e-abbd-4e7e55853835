import { Client, <PERSON>M<PERSON>ber, VoiceChannel } from "discord.js";
import { MusicManager } from "../music/MusicManager.js";

let musicManager: MusicManager | null = null;

export function initializeMusicManager(client: Client): void {
	musicManager = new MusicManager(client);
}

export function getMusicManager(): MusicManager {
	if (!musicManager) {
		throw new Error("Music manager not initialized");
	}
	return musicManager;
}

export function isValidURL(string: string): boolean {
	try {
		new URL(string);
		return true;
	} catch (_) {
		return false;
	}
}

export function isYouTubeURL(url: string): boolean {
	return /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\//.test(url);
}

export function isSpotifyURL(url: string): boolean {
	return /^(https?:\/\/)?(open\.)?spotify\.com\//.test(url);
}

export function extractYouTubeVideoId(url: string): string | null {
	const regex =
		/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
	const match = url.match(regex);
	return match ? match[1] : null;
}

export function extractSpotifyTrackId(url: string): string | null {
	const regex = /spotify\.com\/track\/([a-zA-Z0-9]+)/;
	const match = url.match(regex);
	return match ? match[1] : null;
}

export function checkVoicePermissions(member: GuildMember): {
	inVoiceChannel: boolean;
	voiceChannel: VoiceChannel | null;
	canConnect: boolean;
	canSpeak: boolean;
} {
	const voiceChannel = member.voice.channel as VoiceChannel | null;

	if (!voiceChannel) {
		return {
			inVoiceChannel: false,
			voiceChannel: null,
			canConnect: false,
			canSpeak: false,
		};
	}

	const permissions = voiceChannel.permissionsFor(member.guild.members.me!);

	return {
		inVoiceChannel: true,
		voiceChannel,
		canConnect: permissions?.has("Connect") ?? false,
		canSpeak: permissions?.has("Speak") ?? false,
	};
}

export function formatDuration(ms: number): string {
	const seconds = Math.floor(ms / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.floor(minutes / 60);

	if (hours > 0) {
		return `${hours}:${(minutes % 60).toString().padStart(2, "0")}:${(seconds % 60).toString().padStart(2, "0")}`;
	}
	return `${minutes}:${(seconds % 60).toString().padStart(2, "0")}`;
}

export function formatQueueDuration(tracks: any[]): string {
	const totalMs = tracks.reduce((total, track) => total + track.duration, 0);
	return formatDuration(totalMs);
}

export function createProgressBar(current: number, total: number, length: number = 20): string {
	if (total === 0) return "▬".repeat(length);

	const progress = Math.floor((current / total) * length);
	const bar = "▬".repeat(length);

	return bar.substring(0, progress) + "🔘" + bar.substring(progress + 1);
}

export function parseSpotifyPlaylist(url: string): string | null {
	const regex = /spotify\.com\/playlist\/([a-zA-Z0-9]+)/;
	const match = url.match(regex);
	return match ? match[1] : null;
}

export function parseYouTubePlaylist(url: string): string | null {
	const regex = /[?&]list=([a-zA-Z0-9_-]+)/;
	const match = url.match(regex);
	return match ? match[1] : null;
}

export function sanitizeSearchQuery(query: string): string {
	// Remove special characters that might interfere with search
	return query.replace(/[^\w\s-]/g, "").trim();
}

export function getVolumeEmoji(volume: number): string {
	if (volume === 0) return "🔇";
	if (volume < 30) return "🔈";
	if (volume < 70) return "🔉";
	return "🔊";
}

export function getLoopModeEmoji(loopMode: number): string {
	switch (loopMode) {
		case 0:
			return "➡️"; // No loop
		case 1:
			return "🔂"; // Loop track
		case 2:
			return "🔁"; // Loop queue
		default:
			return "➡️";
	}
}
