import { Action<PERSON>ow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>tonStyle } from "discord.js";
import { emojis } from "../utils/emojis.js";

const lockButton = new ButtonBuilder()
	.setCustomId("ticket-lock-conversation")
	.setLabel("Lock Ticket")
	.setStyle(ButtonStyle.Secondary)
	.setDisabled(false)
	.setEmoji(emojis.ticket.bubble.lock);

const karuButton = new ButtonBuilder()
	.setCustomId("karu-button")
	.setLabel("Kāru AI (Beta)")
	.setStyle(ButtonStyle.Secondary)
	.setDisabled(false)
	.setEmoji(emojis.intelligence);

const ticketButtonRow = new ActionRowBuilder<ButtonBuilder>().addComponents(lockButton, karuButton);

export { ticketButtonRow };
