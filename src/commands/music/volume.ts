import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	GuildMember,
	EmbedBuilder,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const volumeCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("volume")
		.setNameLocalizations({
			tr: "ses",
			"zh-CN": "音量",
			it: "volume",
			"pt-BR": "volume",
		})
		.setDescription("Set or check the music volume")
		.setDescriptionLocalizations({
			tr: "<PERSON><PERSON><PERSON><PERSON> sesini ayarla veya kontrol et",
			"zh-CN": "设置或检查音乐音量",
			it: "Imposta o controlla il volume della musica",
			"pt-BR": "Definir ou verificar o volume da música",
		})
		.addIntegerOption(option =>
			option
				.setName("level")
				.setNameLocalizations({
					tr: "seviye",
					"zh-CN": "级别",
					it: "livello",
					"pt-BR": "nível",
				})
				.setDescription("Volume level (0-100)")
				.setDescriptionLocalizations({
					tr: "Ses seviyesi (0-100)",
					"zh-CN": "音量级别 (0-100)",
					it: "Livello del volume (0-100)",
					"pt-BR": "Nível de volume (0-100)",
				})
				.setMinValue(0)
				.setMaxValue(100),
		) as SlashCommandBuilder,

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild, member, options } = interaction;
		const volumeLevel = options.getInteger("level");

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to use this command!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue) {
			return sendAlertMessage({
				interaction,
				content: "There's no music queue in this server!",
				type: "info",
				tag: "No Queue",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check if bot is in the same voice channel
		if (queue.voiceChannel.id !== voiceChannel.id) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in the same voice channel as me to use this command!",
				type: "error",
				tag: "Different Voice Channel",
			});
		}

		// If no volume level provided, show current volume
		if (volumeLevel === null) {
			const volumeEmoji =
				queue.volume === 0 ? "🔇" : queue.volume < 30 ? "🔈" : queue.volume < 70 ? "🔉" : "🔊";

			const embed = new EmbedBuilder()
				.setTitle(`${volumeEmoji} Current Volume`)
				.setDescription(`Volume is set to **${queue.volume}%**`)
				.setColor(0x0099ff);

			return interaction.reply({
				embeds: [embed],
			});
		}

		try {
			await queue.setVolume(volumeLevel);

			const volumeEmoji =
				volumeLevel === 0 ? "🔇" : volumeLevel < 30 ? "🔈" : volumeLevel < 70 ? "🔉" : "🔊";

			const embed = new EmbedBuilder()
				.setTitle(`${volumeEmoji} Volume Updated`)
				.setDescription(`Volume set to **${volumeLevel}%**`)
				.setColor(0x00ff00);

			return interaction.reply({
				embeds: [embed],
			});
		} catch (error) {
			console.error("Volume command error:", error);
			return sendAlertMessage({
				interaction,
				content: "Failed to set the volume.",
				type: "error",
				tag: "Volume Failed",
			});
		}
	},
};

export default volumeCommand;
