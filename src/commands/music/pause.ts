import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	GuildMember,
	EmbedBuilder,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const pauseCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("pause")
		.setNameLocalizations({
			tr: "duraklat",
			"zh-CN": "暂停",
			it: "pausa",
			"pt-BR": "pausar",
		})
		.setDescription("Pause or resume the current track")
		.setDescriptionLocalizations({
			tr: "Mevcut şarkıyı duraklat veya devam ettir",
			"zh-CN": "暂停或恢复当前曲目",
			it: "Metti in pausa o riprendi la traccia corrente",
			"pt-BR": "Pausar ou retomar a faixa atual",
		}),

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild, member } = interaction;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to use this command!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue || !queue.currentTrack) {
			return sendAlertMessage({
				interaction,
				content: "There's nothing playing right now!",
				type: "info",
				tag: "Nothing Playing",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check if bot is in the same voice channel
		if (queue.voiceChannel.id !== voiceChannel.id) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in the same voice channel as me to use this command!",
				type: "error",
				tag: "Different Voice Channel",
			});
		}

		try {
			if (queue.paused) {
				await queue.resume();
				const embed = new EmbedBuilder()
					.setTitle("▶️ Music Resumed")
					.setDescription(`Resumed: **${queue.currentTrack.title}**`)
					.setColor(0x00ff00);

				return interaction.reply({
					embeds: [embed],
				});
			} else {
				await queue.pause();
				const embed = new EmbedBuilder()
					.setTitle("⏸️ Music Paused")
					.setDescription(`Paused: **${queue.currentTrack.title}**`)
					.setColor(0xffaa00);

				return interaction.reply({
					embeds: [embed],
				});
			}
		} catch (error) {
			console.error("Pause command error:", error);
			return sendAlertMessage({
				interaction,
				content: "Failed to pause/resume the track.",
				type: "error",
				tag: "Pause Failed",
			});
		}
	},
};

export default pauseCommand;
