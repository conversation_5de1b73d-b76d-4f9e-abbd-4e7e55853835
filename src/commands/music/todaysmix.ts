import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	GuildMember,
	VoiceChannel,
	PermissionFlagsBits,
	EmbedBuilder,
	ActionRowBuilder,
	ButtonBuilder,
	ButtonStyle,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";
import { PlaylistCategory } from "../../music/DailyPlaylistManager.js";

const todaysMixCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("todaysmix")
		.setNameLocalizations({
			tr: "gün<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ım",
			"zh-CN": "今日混音",
			it: "mixdioggi",
			"pt-BR": "mixdehoje",
		})
		.setDescription("Access today's curated playlists")
		.setDescriptionLocalizations({
			tr: "Bugünün özenle seçilmiş çalma listelerine erişin",
			"zh-CN": "访问今日精选播放列表",
			it: "Accedi alle playlist curate di oggi",
			"pt-BR": "Acesse as playlists selecionadas de hoje",
		})
		.addStringOption(option =>
			option
				.setName("category")
				.setNameLocalizations({
					tr: "kategori",
					"zh-CN": "类别",
					it: "categoria",
					"pt-BR": "categoria",
				})
				.setDescription("Choose a playlist category")
				.setDescriptionLocalizations({
					tr: "Bir çalma listesi kategorisi seçin",
					"zh-CN": "选择播放列表类别",
					it: "Scegli una categoria di playlist",
					"pt-BR": "Escolha uma categoria de playlist",
				})
				.addChoices(
					{ name: "🆕 New Mix", value: PlaylistCategory.NEW_MIX },
					{ name: "😌 Chill", value: PlaylistCategory.CHILL },
					{ name: "⚡ Energetic", value: PlaylistCategory.ENERGETIC },
				),
		)
		.addBooleanOption(option =>
			option
				.setName("shuffle")
				.setNameLocalizations({
					tr: "karıştır",
					"zh-CN": "随机播放",
					it: "mescola",
					"pt-BR": "embaralhar",
				})
				.setDescription("Shuffle the playlist before playing")
				.setDescriptionLocalizations({
					tr: "Çalmadan önce çalma listesini karıştır",
					"zh-CN": "播放前随机播放列表",
					it: "Mescola la playlist prima di riprodurre",
					"pt-BR": "Embaralhar a playlist antes de tocar",
				}),
		) as SlashCommandBuilder,

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild, member, options } = interaction;
		const category = options.getString("category") as PlaylistCategory | null;
		const shuffle = options.getBoolean("shuffle") || false;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to play music!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check bot permissions
		if (
			!voiceChannel
				.permissionsFor(guild.members.me!)
				?.has([PermissionFlagsBits.Connect, PermissionFlagsBits.Speak])
		) {
			return sendAlertMessage({
				interaction,
				content: "I don't have permission to join or speak in your voice channel!",
				type: "error",
				tag: "Missing Permissions",
			});
		}

		await interaction.deferReply();

		try {
			const musicManager = getMusicManager();
			const dailyPlaylistManager = musicManager.dailyPlaylistManager;

			// If no category specified, show category selection
			if (!category) {
				const embed = new EmbedBuilder()
					.setTitle("🎵 Today's Mix")
					.setDescription("Choose a playlist category to start listening!")
					.setColor(0x9932cc);

				// Add info about each category
				for (const cat of Object.values(PlaylistCategory)) {
					const info = dailyPlaylistManager.getPlaylistInfo(guild.id, cat);
					const displayName = dailyPlaylistManager.getCategoryDisplayName(cat);
					const description = dailyPlaylistManager.getCategoryDescription(cat);
					const lastUpdated = info.lastUpdated
						? `Last updated: <t:${Math.floor(info.lastUpdated.getTime() / 1000)}:R>`
						: "Not loaded yet";

					embed.addFields({
						name: displayName,
						value: `${description}\n${info.trackCount} tracks • ${lastUpdated}`,
						inline: false,
					});
				}

				const categoryButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
					new ButtonBuilder()
						.setCustomId(`todaysmix-${PlaylistCategory.NEW_MIX}`)
						.setLabel("New Mix")
						.setEmoji("🆕")
						.setStyle(ButtonStyle.Primary),
					new ButtonBuilder()
						.setCustomId(`todaysmix-${PlaylistCategory.CHILL}`)
						.setLabel("Chill")
						.setEmoji("😌")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder()
						.setCustomId(`todaysmix-${PlaylistCategory.ENERGETIC}`)
						.setLabel("Energetic")
						.setEmoji("⚡")
						.setStyle(ButtonStyle.Success),
				);

				return interaction.editReply({
					embeds: [embed],
					components: [categoryButtons],
				});
			}

			// Get the playlist for the specified category
			const tracks = await dailyPlaylistManager.getPlaylist(guild.id, category);

			if (tracks.length === 0) {
				return interaction.editReply({
					embeds: [
						new EmbedBuilder()
							.setTitle(`${getEmoji("error")} No Tracks Available`)
							.setDescription(
								`No tracks found for ${dailyPlaylistManager.getCategoryDisplayName(category)}. Please try again later.`,
							)
							.setColor(0xff0000),
					],
				});
			}

			// Set requester for all tracks
			tracks.forEach(track => {
				track.requester = interaction.user.id;
			});

			// Shuffle if requested
			let playlistTracks = [...tracks];
			if (shuffle) {
				for (let i = playlistTracks.length - 1; i > 0; i--) {
					const j = Math.floor(Math.random() * (i + 1));
					[playlistTracks[i], playlistTracks[j]] = [playlistTracks[j], playlistTracks[i]];
				}
			}

			const queue = await musicManager.createQueue(
				guild,
				voiceChannel as VoiceChannel,
				interaction.channel as any,
			);

			// If queue is empty and no track is playing, play first track and add rest to queue
			if (!queue.currentTrack && queue.tracks.length === 0) {
				await queue.connect();
				const firstTrack = playlistTracks.shift()!;
				queue.addTracks(playlistTracks);
				await queue.play(firstTrack);

				const embed = new EmbedBuilder()
					.setTitle(`🎵 ${dailyPlaylistManager.getCategoryDisplayName(category)} Started`)
					.setDescription(`Now playing: **${firstTrack.title}**\nBy: ${firstTrack.author}`)
					.setThumbnail(firstTrack.thumbnail)
					.addFields(
						{
							name: "Playlist",
							value: `${tracks.length} tracks${shuffle ? " (shuffled)" : ""}`,
							inline: true,
						},
						{ name: "Queue", value: `${queue.tracks.length} tracks remaining`, inline: true },
					)
					.setColor(0x00ff00);

				const controlButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
					new ButtonBuilder()
						.setCustomId("music-previous")
						.setEmoji("⏮️")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder()
						.setCustomId("music-pause")
						.setEmoji("⏸️")
						.setStyle(ButtonStyle.Primary),
					new ButtonBuilder()
						.setCustomId("music-skip")
						.setEmoji("⏭️")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder().setCustomId("music-stop").setEmoji("⏹️").setStyle(ButtonStyle.Danger),
				);

				return interaction.editReply({
					embeds: [embed],
					components: [controlButtons],
				});
			} else {
				// Add all tracks to existing queue
				queue.addTracks(playlistTracks);

				const embed = new EmbedBuilder()
					.setTitle(`📝 ${dailyPlaylistManager.getCategoryDisplayName(category)} Added to Queue`)
					.setDescription(
						`Added **${tracks.length}** tracks to the queue${shuffle ? " (shuffled)" : ""}`,
					)
					.addFields(
						{
							name: "Queue position",
							value: `Starting at position ${queue.tracks.length - tracks.length + 1}`,
							inline: true,
						},
						{ name: "Total queue", value: `${queue.tracks.length} tracks`, inline: true },
					)
					.setColor(0x0099ff);

				return interaction.editReply({
					embeds: [embed],
				});
			}
		} catch (error) {
			console.error("Today's Mix command error:", error);
			return interaction.editReply({
				embeds: [
					new EmbedBuilder()
						.setTitle(`${getEmoji("error")} Error`)
						.setDescription("An error occurred while loading the playlist.")
						.setColor(0xff0000),
				],
			});
		}
	},
};

export default todaysMixCommand;
