import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	GuildMember,
	EmbedBuilder,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const stopCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("stop")
		.setNameLocalizations({
			tr: "durdur",
			"zh-CN": "停止",
			it: "ferma",
			"pt-BR": "parar",
		})
		.setDescription("Stop the music and clear the queue")
		.setDescriptionLocalizations({
			tr: "<PERSON><PERSON><PERSON><PERSON><PERSON> durdur ve kuyruğu temizle",
			"zh-CN": "停止音乐并清空队列",
			it: "Ferma la musica e svuota la coda",
			"pt-BR": "Parar a música e limpar a fila",
		}),

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild, member } = interaction;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to use this command!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue) {
			return sendAlertMessage({
				interaction,
				content: "There's no music queue in this server!",
				type: "info",
				tag: "No Queue",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check if bot is in the same voice channel
		if (queue.voiceChannel.id !== voiceChannel.id) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in the same voice channel as me to use this command!",
				type: "error",
				tag: "Different Voice Channel",
			});
		}

		try {
			await queue.stop();
			queue.destroy();

			const embed = new EmbedBuilder()
				.setTitle("⏹️ Music Stopped")
				.setDescription("Music stopped and queue cleared. Disconnected from voice channel.")
				.setColor(0xff0000);

			return interaction.reply({
				embeds: [embed],
			});
		} catch (error) {
			console.error("Stop command error:", error);
			return sendAlertMessage({
				interaction,
				content: "Failed to stop the music.",
				type: "error",
				tag: "Stop Failed",
			});
		}
	},
};

export default stopCommand;
