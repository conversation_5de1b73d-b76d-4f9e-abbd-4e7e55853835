import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	GuildMember,
	EmbedBuilder,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const skipCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("skip")
		.setNameLocalizations({
			tr: "geç",
			"zh-CN": "跳过",
			it: "salta",
			"pt-BR": "pular",
		})
		.setDescription("Skip the current track")
		.setDescriptionLocalizations({
			tr: "Mevcut şarkıyı geç",
			"zh-CN": "跳过当前曲目",
			it: "Salta la traccia corrente",
			"pt-BR": "Pular a faixa atual",
		}),

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild, member } = interaction;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to use this command!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue || !queue.currentTrack) {
			return sendAlertMessage({
				interaction,
				content: "There's nothing playing right now!",
				type: "info",
				tag: "Nothing Playing",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check if bot is in the same voice channel
		if (queue.voiceChannel.id !== voiceChannel.id) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in the same voice channel as me to use this command!",
				type: "error",
				tag: "Different Voice Channel",
			});
		}

		const skippedTrack = queue.currentTrack;
		const success = await queue.skip();

		if (success) {
			const embed = new EmbedBuilder()
				.setTitle("⏭️ Track Skipped")
				.setDescription(`Skipped: **${skippedTrack.title}**`)
				.setColor(0x00ff00);

			return interaction.reply({
				embeds: [embed],
			});
		} else {
			return sendAlertMessage({
				interaction,
				content: "Failed to skip the track.",
				type: "error",
				tag: "Skip Failed",
			});
		}
	},
};

export default skipCommand;
