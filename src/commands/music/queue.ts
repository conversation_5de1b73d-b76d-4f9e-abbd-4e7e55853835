import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	EmbedBuilder,
	ActionRowBuilder,
	ButtonBuilder,
	ButtonStyle,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager, formatQueueDuration } from "../../utils/musicUtils.js";

const queueCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("queue")
		.setNameLocalizations({
			tr: "kuyruk",
			"zh-CN": "队列",
			it: "coda",
			"pt-BR": "fila",
		})
		.setDescription("Show the current music queue")
		.setDescriptionLocalizations({
			tr: "Mevcut müzik k<PERSON>",
			"zh-CN": "显示当前音乐队列",
			it: "Mostra la coda musicale corrente",
			"pt-BR": "Mostrar a fila de música atual",
		})
		.addIntegerOption(option =>
			option
				.setName("page")
				.setNameLocalizations({
					tr: "sayfa",
					"zh-CN": "页面",
					it: "pagina",
					"pt-BR": "página",
				})
				.setDescription("Page number to display")
				.setDescriptionLocalizations({
					tr: "Gösterilecek sayfa numarası",
					"zh-CN": "要显示的页码",
					it: "Numero di pagina da visualizzare",
					"pt-BR": "Número da página para exibir",
				})
				.setMinValue(1),
		) as SlashCommandBuilder,

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild, options } = interaction;
		const page = options.getInteger("page") || 1;

		if (!guild) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue) {
			return sendAlertMessage({
				interaction,
				content: "There's no music queue in this server!",
				type: "info",
				tag: "No Queue",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const tracksPerPage = 10;
		const totalPages = Math.ceil(queue.tracks.length / tracksPerPage);
		const startIndex = (page - 1) * tracksPerPage;
		const endIndex = startIndex + tracksPerPage;
		const currentPageTracks = queue.tracks.slice(startIndex, endIndex);

		const embed = new EmbedBuilder().setTitle("🎵 Music Queue").setColor(0x0099ff);

		// Current track info
		if (queue.currentTrack) {
			embed.addFields({
				name: "🎵 Now Playing",
				value: `**${queue.currentTrack.getDisplayTitle()}**\nBy: ${queue.currentTrack.getDisplayAuthor()}\nDuration: ${queue.currentTrack.getFormattedDuration()}\nRequested by: <@${queue.currentTrack.requester}>`,
				inline: false,
			});
		}

		// Queue info
		if (queue.tracks.length === 0) {
			embed.addFields({
				name: "📝 Queue",
				value: "The queue is empty. Add some tracks with `/play`!",
				inline: false,
			});
		} else {
			const queueList = currentPageTracks
				.map((track, index) => {
					const position = startIndex + index + 1;
					return `**${position}.** ${track.getDisplayTitle()}\n   By: ${track.getDisplayAuthor()} | ${track.getFormattedDuration()} | <@${track.requester}>`;
				})
				.join("\n\n");

			embed.addFields({
				name: `📝 Queue (${queue.tracks.length} tracks)`,
				value: queueList,
				inline: false,
			});

			// Queue statistics
			const totalDuration = formatQueueDuration(queue.tracks);
			embed.addFields({
				name: "📊 Queue Info",
				value: `Total Duration: ${totalDuration}\nPage: ${page}/${totalPages || 1}`,
				inline: true,
			});

			// Player info
			embed.addFields({
				name: "🎛️ Player Info",
				value: `Volume: ${queue.volume}%\nLoop: ${queue.loop === 0 ? "Off" : queue.loop === 1 ? "Track" : "Queue"}\nStatus: ${queue.paused ? "Paused" : "Playing"}`,
				inline: true,
			});
		}

		// Navigation buttons
		const components: ActionRowBuilder<ButtonBuilder>[] = [];

		if (totalPages > 1) {
			const navigationRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
				new ButtonBuilder()
					.setCustomId(`queue-page-${page - 1}`)
					.setLabel("Previous")
					.setEmoji("⬅️")
					.setStyle(ButtonStyle.Secondary)
					.setDisabled(page <= 1),
				new ButtonBuilder()
					.setCustomId(`queue-page-${page + 1}`)
					.setLabel("Next")
					.setEmoji("➡️")
					.setStyle(ButtonStyle.Secondary)
					.setDisabled(page >= totalPages),
			);
			components.push(navigationRow);
		}

		// Control buttons
		if (queue.currentTrack) {
			const controlRow = new ActionRowBuilder<ButtonBuilder>().addComponents(
				new ButtonBuilder()
					.setCustomId("music-previous")
					.setEmoji("⏮️")
					.setStyle(ButtonStyle.Secondary),
				new ButtonBuilder()
					.setCustomId(queue.paused ? "music-resume" : "music-pause")
					.setEmoji(queue.paused ? "▶️" : "⏸️")
					.setStyle(ButtonStyle.Primary),
				new ButtonBuilder()
					.setCustomId("music-skip")
					.setEmoji("⏭️")
					.setStyle(ButtonStyle.Secondary),
				new ButtonBuilder().setCustomId("music-stop").setEmoji("⏹️").setStyle(ButtonStyle.Danger),
				new ButtonBuilder()
					.setCustomId("queue-clear")
					.setLabel("Clear")
					.setEmoji("🗑️")
					.setStyle(ButtonStyle.Danger),
			);
			components.push(controlRow);
		}

		return interaction.reply({
			embeds: [embed],
			components,
		});
	},
};

export default queueCommand;
