import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	EmbedBuilder,
	ActionRowBuilder,
	ButtonBuilder,
	ButtonStyle,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager, createProgressBar } from "../../utils/musicUtils.js";

const nowPlayingCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("nowplaying")
		.setNameLocalizations({
			tr: "şuançalan",
			"zh-CN": "正在播放",
			it: "inriproduzione",
			"pt-BR": "tocandoagora",
		})
		.setDescription("Show information about the currently playing track")
		.setDescriptionLocalizations({
			tr: "Şu anda çalan şarkı hakkında bilgi göster",
			"zh-CN": "显示当前播放曲目的信息",
			it: "Mostra informazioni sulla traccia in riproduzione",
			"pt-BR": "Mostrar informações sobre a faixa atual",
		}),

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild } = interaction;

		if (!guild) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue || !queue.currentTrack) {
			return sendAlertMessage({
				interaction,
				content: "There's nothing playing right now!",
				type: "info",
				tag: "Nothing Playing",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const track = queue.currentTrack;
		const player = queue.player;

		// Get current position (if available from player)
		const currentPosition = player?.position || 0;
		const progressBar = createProgressBar(currentPosition, track.duration);

		const embed = new EmbedBuilder()
			.setTitle("🎵 Now Playing")
			.setDescription(`**${track.title}**\nBy: ${track.author}`)
			.setThumbnail(track.thumbnail)
			.setURL(track.uri)
			.addFields(
				{
					name: "⏱️ Duration",
					value: track.isLiveStream() ? "🔴 Live Stream" : track.getFormattedDuration(),
					inline: true,
				},
				{
					name: "👤 Requested by",
					value: `<@${track.requester}>`,
					inline: true,
				},
				{
					name: "🔊 Volume",
					value: `${queue.volume}%`,
					inline: true,
				},
				{
					name: "🔁 Loop Mode",
					value: queue.loop === 0 ? "Off" : queue.loop === 1 ? "Track" : "Queue",
					inline: true,
				},
				{
					name: "📊 Status",
					value: queue.paused ? "⏸️ Paused" : "▶️ Playing",
					inline: true,
				},
				{
					name: "📝 Queue",
					value: `${queue.tracks.length} tracks remaining`,
					inline: true,
				},
			)
			.setColor(queue.paused ? 0xffaa00 : 0x00ff00);

		// Add progress bar if not a live stream
		if (!track.isLiveStream() && track.duration > 0) {
			const currentTime = Math.floor(currentPosition / 1000);
			const totalTime = Math.floor(track.duration / 1000);
			const currentFormatted = `${Math.floor(currentTime / 60)}:${(currentTime % 60).toString().padStart(2, "0")}`;
			const totalFormatted = track.getFormattedDuration();

			embed.addFields({
				name: "⏯️ Progress",
				value: `${currentFormatted} ${progressBar} ${totalFormatted}`,
				inline: false,
			});
		}

		// Control buttons
		const controlButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
			new ButtonBuilder()
				.setCustomId("music-previous")
				.setEmoji("⏮️")
				.setStyle(ButtonStyle.Secondary)
				.setDisabled(queue.previousTracks.length === 0),
			new ButtonBuilder()
				.setCustomId(queue.paused ? "music-resume" : "music-pause")
				.setEmoji(queue.paused ? "▶️" : "⏸️")
				.setStyle(ButtonStyle.Primary),
			new ButtonBuilder().setCustomId("music-skip").setEmoji("⏭️").setStyle(ButtonStyle.Secondary),
			new ButtonBuilder().setCustomId("music-stop").setEmoji("⏹️").setStyle(ButtonStyle.Danger),
			new ButtonBuilder()
				.setCustomId("music-loop")
				.setEmoji("🔁")
				.setStyle(queue.loop === 0 ? ButtonStyle.Secondary : ButtonStyle.Success),
		);

		// Volume control buttons
		const volumeButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
			new ButtonBuilder()
				.setCustomId("volume-down")
				.setEmoji("🔉")
				.setStyle(ButtonStyle.Secondary)
				.setDisabled(queue.volume <= 0),
			new ButtonBuilder()
				.setCustomId("volume-up")
				.setEmoji("🔊")
				.setStyle(ButtonStyle.Secondary)
				.setDisabled(queue.volume >= 100),
			new ButtonBuilder()
				.setCustomId("music-shuffle")
				.setEmoji("🔀")
				.setStyle(ButtonStyle.Secondary)
				.setDisabled(queue.tracks.length <= 1),
			new ButtonBuilder()
				.setCustomId("queue-show")
				.setLabel("Queue")
				.setEmoji("📝")
				.setStyle(ButtonStyle.Secondary),
		);

		return interaction.reply({
			embeds: [embed],
			components: [controlButtons, volumeButtons],
		});
	},
};

export default nowPlayingCommand;
