import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	GuildMember,
	EmbedBuilder,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const clearCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("clear")
		.setNameLocalizations({
			tr: "temizle",
			"zh-CN": "清空",
			it: "pulisci",
			"pt-BR": "limpar",
		})
		.setDescription("Clear the music queue")
		.setDescriptionLocalizations({
			tr: "<PERSON><PERSON><PERSON><PERSON> k<PERSON> temizle",
			"zh-CN": "清空音乐队列",
			it: "Pulisci la coda musicale",
			"pt-BR": "Limpar a fila de música",
		}),

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild, member } = interaction;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to use this command!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue) {
			return sendAlertMessage({
				interaction,
				content: "There's no music queue in this server!",
				type: "info",
				tag: "No Queue",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check if bot is in the same voice channel
		if (queue.voiceChannel.id !== voiceChannel.id) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in the same voice channel as me to use this command!",
				type: "error",
				tag: "Different Voice Channel",
			});
		}

		if (queue.tracks.length === 0) {
			return sendAlertMessage({
				interaction,
				content: "The queue is already empty!",
				type: "info",
				tag: "Empty Queue",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const clearedCount = queue.tracks.length;
		queue.clearQueue();

		const embed = new EmbedBuilder()
			.setTitle("🗑️ Queue Cleared")
			.setDescription(`Cleared **${clearedCount}** tracks from the queue.`)
			.setColor(0xff6600);

		return interaction.reply({
			embeds: [embed],
		});
	},
};

export default clearCommand;
