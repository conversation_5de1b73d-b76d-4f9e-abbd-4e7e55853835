import {
	ApplicationIntegrationType,
	ChatInputCommandInteraction,
	InteractionContextType,
	SlashCommandBuilder,
	GuildMember,
	VoiceChannel,
	PermissionFlagsBits,
	EmbedBuilder,
	ActionRowBuilder,
	ButtonBuilder,
	ButtonStyle,
} from "discord.js";
import { BotCommand } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const playCommand: BotCommand = {
	data: new SlashCommandBuilder()
		.setContexts(InteractionContextType.Guild)
		.setIntegrationTypes(ApplicationIntegrationType.GuildInstall)
		.setName("play")
		.setNameLocalizations({
			tr: "çal",
			"zh-CN": "播放",
			it: "riproduci",
			"pt-BR": "tocar",
		})
		.setDescription("Play music from YouTube or Spotify")
		.setDescriptionLocalizations({
			tr: "YouTube veya Spotify'dan müzik çal",
			"zh-CN": "从YouTube或Spotify播放音乐",
			it: "Riproduci musica da YouTube o Spotify",
			"pt-BR": "Tocar música do YouTube ou Spotify",
		})
		.addStringOption(option =>
			option
				.setName("query")
				.setNameLocalizations({
					tr: "sorgu",
					"zh-CN": "查询",
					it: "ricerca",
					"pt-BR": "consulta",
				})
				.setDescription("Song name, YouTube URL, or Spotify URL")
				.setDescriptionLocalizations({
					tr: "Şarkı adı, YouTube URL'si veya Spotify URL'si",
					"zh-CN": "歌曲名称、YouTube链接或Spotify链接",
					it: "Nome della canzone, URL di YouTube o URL di Spotify",
					"pt-BR": "Nome da música, URL do YouTube ou URL do Spotify",
				})
				.setRequired(true),
		) as SlashCommandBuilder,

	execute: async (interaction: ChatInputCommandInteraction) => {
		const { guild, member, options } = interaction;
		const query = options.getString("query", true);

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to play music!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check bot permissions
		if (
			!voiceChannel
				.permissionsFor(guild.members.me!)
				?.has([PermissionFlagsBits.Connect, PermissionFlagsBits.Speak])
		) {
			return sendAlertMessage({
				interaction,
				content: "I don't have permission to join or speak in your voice channel!",
				type: "error",
				tag: "Missing Permissions",
			});
		}

		await interaction.deferReply();

		try {
			const musicManager = getMusicManager();
			const queue = await musicManager.createQueue(
				guild,
				voiceChannel as VoiceChannel,
				interaction.channel as any,
			);

			// Search for tracks
			const tracks = await musicManager.searchTracks(query);

			if (tracks.length === 0) {
				return interaction.editReply({
					embeds: [
						new EmbedBuilder()
							.setTitle(`${getEmoji("error")} No Results`)
							.setDescription("No tracks found for your search query.")
							.setColor(0xff0000),
					],
				});
			}

			// Set requester for the track
			tracks[0].requester = interaction.user.id;

			// If queue is empty and no track is playing, play immediately
			if (!queue.currentTrack && queue.tracks.length === 0) {
				await queue.connect();
				await queue.play(tracks[0]);

				const embed = new EmbedBuilder()
					.setTitle("🎵 Now Playing")
					.setDescription(`**${tracks[0].title}**\nBy: ${tracks[0].author}`)
					.setThumbnail(tracks[0].thumbnail)
					.addFields(
						{ name: "Duration", value: tracks[0].getFormattedDuration(), inline: true },
						{ name: "Requested by", value: `<@${interaction.user.id}>`, inline: true },
					)
					.setColor(0x00ff00);

				const controlButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
					new ButtonBuilder()
						.setCustomId("music-previous")
						.setEmoji("⏮️")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder()
						.setCustomId("music-pause")
						.setEmoji("⏸️")
						.setStyle(ButtonStyle.Primary),
					new ButtonBuilder()
						.setCustomId("music-skip")
						.setEmoji("⏭️")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder().setCustomId("music-stop").setEmoji("⏹️").setStyle(ButtonStyle.Danger),
				);

				return interaction.editReply({
					embeds: [embed],
					components: [controlButtons],
				});
			} else {
				// Add to queue
				queue.addTrack(tracks[0]);

				const embed = new EmbedBuilder()
					.setTitle("📝 Added to Queue")
					.setDescription(`**${tracks[0].title}**\nBy: ${tracks[0].author}`)
					.setThumbnail(tracks[0].thumbnail)
					.addFields(
						{ name: "Duration", value: tracks[0].getFormattedDuration(), inline: true },
						{ name: "Position in queue", value: `${queue.tracks.length}`, inline: true },
						{ name: "Requested by", value: `<@${interaction.user.id}>`, inline: true },
					)
					.setColor(0x0099ff);

				return interaction.editReply({
					embeds: [embed],
				});
			}
		} catch (error) {
			console.error("Play command error:", error);
			return interaction.editReply({
				embeds: [
					new EmbedBuilder()
						.setTitle(`${getEmoji("error")} Error`)
						.setDescription("An error occurred while trying to play the track.")
						.setColor(0xff0000),
				],
			});
		}
	},
};

export default playCommand;
