import { ButtonInteraction, GuildMember, EmbedBuilder } from "discord.js";
import { BotComponent } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const musicShuffle: BotComponent = {
	customId: "music-shuffle",

	execute: async (interaction: ButtonInteraction) => {
		const { guild, member } = interaction;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to use this command!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue) {
			return sendAlertMessage({
				interaction,
				content: "There's no music queue in this server!",
				type: "info",
				tag: "No Queue",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check if bot is in the same voice channel
		if (queue.voiceChannel.id !== voiceChannel.id) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in the same voice channel as me to use this command!",
				type: "error",
				tag: "Different Voice Channel",
			});
		}

		if (queue.tracks.length <= 1) {
			return sendAlertMessage({
				interaction,
				content: "Need at least 2 tracks in the queue to shuffle!",
				type: "info",
				tag: "Not Enough Tracks",
				alertReaction: "reactions.kaeru.question",
			});
		}

		queue.shuffle();

		const embed = new EmbedBuilder()
			.setTitle("🔀 Queue Shuffled")
			.setDescription(`Shuffled **${queue.tracks.length}** tracks in the queue.`)
			.setColor(0x9932cc);

		return interaction.reply({
			embeds: [embed],
			ephemeral: true,
		});
	},
};

export default musicShuffle;
