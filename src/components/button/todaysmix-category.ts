import {
	ButtonInteraction,
	GuildMember,
	VoiceChannel,
	PermissionFlagsBits,
	EmbedBuilder,
	ActionRowBuilder,
	ButtonBuilder,
	ButtonStyle,
	InteractionResponse,
} from "discord.js";
import { BotComponent } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";
import { PlaylistCategory } from "../../music/DailyPlaylistManager.js";

const todaysMixCategory: BotComponent = {
	customId: /^todaysmix-(new_mix|chill|energetic)$/,

	execute: async (interaction: ButtonInteraction): Promise<InteractionResponse<boolean> | void> => {
		const { guild, member, customId } = interaction;
		const category = customId.split("-")[1] as PlaylistCategory;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to play music!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check bot permissions
		if (
			!voiceChannel
				.permissionsFor(guild.members.me!)
				?.has([PermissionFlagsBits.Connect, PermissionFlagsBits.Speak])
		) {
			return sendAlertMessage({
				interaction,
				content: "I don't have permission to join or speak in your voice channel!",
				type: "error",
				tag: "Missing Permissions",
			});
		}

		await interaction.deferReply();

		try {
			const musicManager = getMusicManager();
			const dailyPlaylistManager = musicManager.dailyPlaylistManager;

			// Get the playlist for the specified category
			const tracks = await dailyPlaylistManager.getPlaylist(guild.id, category);

			if (tracks.length === 0) {
				await interaction.editReply({
					embeds: [
						new EmbedBuilder()
							.setTitle(`${getEmoji("error")} No Tracks Available`)
							.setDescription(
								`No tracks found for ${dailyPlaylistManager.getCategoryDisplayName(category)}. Please try again later.`,
							)
							.setColor(0xff0000),
					],
				});
			}

			// Set requester for all tracks
			tracks.forEach(track => {
				track.requester = interaction.user.id;
			});

			const queue = await musicManager.createQueue(
				guild,
				voiceChannel as VoiceChannel,
				interaction.channel as any,
			);

			// If queue is empty and no track is playing, play first track and add rest to queue
			if (!queue.currentTrack && queue.tracks.length === 0) {
				await queue.connect();
				const firstTrack = tracks.shift()!;
				queue.addTracks(tracks);
				await queue.play(firstTrack);

				const embed = new EmbedBuilder()
					.setTitle(`🎵 ${dailyPlaylistManager.getCategoryDisplayName(category)} Started`)
					.setDescription(`Now playing: **${firstTrack.title}**\nBy: ${firstTrack.author}`)
					.setThumbnail(firstTrack.thumbnail)
					.addFields(
						{ name: "Playlist", value: `${tracks.length + 1} tracks`, inline: true },
						{ name: "Queue", value: `${queue.tracks.length} tracks remaining`, inline: true },
					)
					.setColor(0x00ff00);

				const controlButtons = new ActionRowBuilder<ButtonBuilder>().addComponents(
					new ButtonBuilder()
						.setCustomId("music-previous")
						.setEmoji("⏮️")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder()
						.setCustomId("music-pause")
						.setEmoji("⏸️")
						.setStyle(ButtonStyle.Primary),
					new ButtonBuilder()
						.setCustomId("music-skip")
						.setEmoji("⏭️")
						.setStyle(ButtonStyle.Secondary),
					new ButtonBuilder().setCustomId("music-stop").setEmoji("⏹️").setStyle(ButtonStyle.Danger),
				);

				await interaction.editReply({
					embeds: [embed],
					components: [controlButtons],
				});
			} else {
				// Add all tracks to existing queue
				queue.addTracks(tracks);

				const embed = new EmbedBuilder()
					.setTitle(`📝 ${dailyPlaylistManager.getCategoryDisplayName(category)} Added to Queue`)
					.setDescription(`Added **${tracks.length}** tracks to the queue`)
					.addFields(
						{
							name: "Queue position",
							value: `Starting at position ${queue.tracks.length - tracks.length + 1}`,
							inline: true,
						},
						{ name: "Total queue", value: `${queue.tracks.length} tracks`, inline: true },
					)
					.setColor(0x0099ff);

				await interaction.editReply({
					embeds: [embed],
				});
			}
		} catch (error) {
			console.error("Today's Mix category button error:", error);
			await interaction.editReply({
				embeds: [
					new EmbedBuilder()
						.setTitle(`${getEmoji("error")} Error`)
						.setDescription("An error occurred while loading the playlist.")
						.setColor(0xff0000),
				],
			});
		}
	},
};

export default todaysMixCategory;
