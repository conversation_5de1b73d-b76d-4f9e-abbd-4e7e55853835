import { ButtonInteraction, GuildMember, EmbedBuilder } from "discord.js";
import { BotComponent } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const musicVolume: BotComponent = {
	customId: /^volume-(up|down)$/,

	execute: async (interaction: ButtonInteraction) => {
		const { guild, member, customId } = interaction;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to use this command!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue) {
			return sendAlertMessage({
				interaction,
				content: "There's no music queue in this server!",
				type: "info",
				tag: "No Queue",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check if bot is in the same voice channel
		if (queue.voiceChannel.id !== voiceChannel.id) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in the same voice channel as me to use this command!",
				type: "error",
				tag: "Different Voice Channel",
			});
		}

		try {
			const isVolumeUp = customId === "volume-up";
			const currentVolume = queue.volume;
			const newVolume = isVolumeUp 
				? Math.min(100, currentVolume + 10)
				: Math.max(0, currentVolume - 10);

			if (newVolume === currentVolume) {
				return sendAlertMessage({
					interaction,
					content: `Volume is already at ${isVolumeUp ? "maximum" : "minimum"}!`,
					type: "info",
					tag: "Volume Limit",
				});
			}

			await queue.setVolume(newVolume);
			
			const volumeEmoji = newVolume === 0 ? "🔇" : newVolume < 30 ? "🔈" : newVolume < 70 ? "🔉" : "🔊";
			
			const embed = new EmbedBuilder()
				.setTitle(`${volumeEmoji} Volume ${isVolumeUp ? "Increased" : "Decreased"}`)
				.setDescription(`Volume set to **${newVolume}%**`)
				.setColor(0x0099ff);

			return interaction.reply({
				embeds: [embed],
				ephemeral: true,
			});
		} catch (error) {
			console.error("Volume command error:", error);
			return sendAlertMessage({
				interaction,
				content: "Failed to adjust the volume.",
				type: "error",
				tag: "Volume Failed",
			});
		}
	},
};

export default musicVolume;
