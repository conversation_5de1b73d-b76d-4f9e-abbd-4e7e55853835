import { ButtonInteraction, GuildMember, EmbedBuilder } from "discord.js";
import { BotComponent } from "../../interfaces/botTypes.js";
import { sendAlertMessage, getEmoji } from "../../utils/export.js";
import { getMusicManager } from "../../utils/musicUtils.js";

const musicPause: BotComponent = {
	customId: /^music-(pause|resume)$/,

	execute: async (interaction: ButtonInteraction) => {
		const { guild, member, customId } = interaction;

		if (!guild || !member) {
			return sendAlertMessage({
				interaction,
				content: "This command can only be used in a server.",
				type: "error",
				tag: "Guild Only",
			});
		}

		// Check if user is in a voice channel
		const voiceChannel = (member as GuildMember).voice.channel;
		if (!voiceChannel) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in a voice channel to use this command!",
				type: "error",
				tag: "No Voice Channel",
				alertReaction: "reactions.kaeru.question",
			});
		}

		const musicManager = getMusicManager();
		const queue = musicManager.getQueue(guild.id);

		if (!queue || !queue.currentTrack) {
			return sendAlertMessage({
				interaction,
				content: "There's nothing playing right now!",
				type: "info",
				tag: "Nothing Playing",
				alertReaction: "reactions.kaeru.question",
			});
		}

		// Check if bot is in the same voice channel
		if (queue.voiceChannel.id !== voiceChannel.id) {
			return sendAlertMessage({
				interaction,
				content: "You need to be in the same voice channel as me to use this command!",
				type: "error",
				tag: "Different Voice Channel",
			});
		}

		try {
			const isPauseAction = customId === "music-pause";
			
			if (isPauseAction && !queue.paused) {
				await queue.pause();
				const embed = new EmbedBuilder()
					.setTitle("⏸️ Music Paused")
					.setDescription(`Paused: **${queue.currentTrack.title}**`)
					.setColor(0xffaa00);

				return interaction.reply({
					embeds: [embed],
					ephemeral: true,
				});
			} else if (!isPauseAction && queue.paused) {
				await queue.resume();
				const embed = new EmbedBuilder()
					.setTitle("▶️ Music Resumed")
					.setDescription(`Resumed: **${queue.currentTrack.title}**`)
					.setColor(0x00ff00);

				return interaction.reply({
					embeds: [embed],
					ephemeral: true,
				});
			} else {
				return sendAlertMessage({
					interaction,
					content: `Music is already ${queue.paused ? "paused" : "playing"}!`,
					type: "info",
					tag: "Already " + (queue.paused ? "Paused" : "Playing"),
				});
			}
		} catch (error) {
			console.error("Pause/Resume command error:", error);
			return sendAlertMessage({
				interaction,
				content: "Failed to pause/resume the track.",
				type: "error",
				tag: "Pause/Resume Failed",
			});
		}
	},
};

export default musicPause;
