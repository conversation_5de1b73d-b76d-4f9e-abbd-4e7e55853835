# 🔐 Security & Abuse Reporting Policy

Thank you for helping keep <PERSON><PERSON><PERSON> and its surrounding ecosystem safe.

Kaeru operates both as an open-source bot and as a moderation system across Discord servers. If you discover a security vulnerability, a misuse of <PERSON><PERSON><PERSON>’s permissions, or abusive behavior tied to Kaeru-powered systems, please follow the guidelines below.

---

## 📥 Vulnerability Disclosure

If you find a vulnerability in <PERSON><PERSON><PERSON>’s codebase, especially those that could affect user privacy, abuse automation, or cause platform-wide disruption:

- Discord: [Kaeru Support Server](https://discord.gg/6ZQWypCgvk)
- Response SLA: We aim to respond within **48 hours**.

Please provide:

- A clear description of the issue
- Steps to reproduce (if possible)
- Impact scope

Do **not** publicly disclose the vulnerability before it has been patched.

---

## 🛡️ Bot Permission Scope

Kaeru follows **minimum-permission principles**.

You can always audit the following:

- Slash command permissions: `/commands`
- Role requirements: `@Kaeru` should not have Administrator unless explicitly needed
- Log channels for action tracking (enable via `/setup logs`)

---

## 🤖 Impersonation or Clone Detection

If you encounter a fake or malicious bot claiming to be <PERSON><PERSON><PERSON>:

- Report the bot ID to us immediately
- Do **not** invite unknown Kaeru forks into your server

---

## 🧠 Security Philosophy

Kaeru is designed to empower, not exploit. We favor:

- Transparency over obscurity
- Auditability over blind trust
- Community-driven safeguards

We appreciate any effort that contributes to a safer platform for all users.
