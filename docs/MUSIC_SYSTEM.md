# Music System Documentation

## Overview

The Kaeru Discord bot now includes a comprehensive music system with voice capabilities, built using Lavalink for audio processing and @discordjs/voice for Discord voice channel connections. The system supports YouTube and Spotify link parsing and playback.

## Features

### Core Playback Controls

- **Play Command**: `/play <query>` - Play music from YouTube/Spotify URLs or search queries
- **Skip**: `/skip` - Skip to the next song in the queue
- **Previous**: Go back to the previous song (if available)
- **Pause/Resume**: `/pause` - Toggle playback state
- **Stop**: `/stop` - Stop music and clear the queue
- **Volume Control**: `/volume [0-100]` - Adjust or check the current volume

### Queue Management

- **Queue Display**: `/queue [page]` - Show the current music queue with pagination
- **Now Playing**: `/nowplaying` - Display detailed information about the current track
- **Clear Queue**: `/clear` - Remove all tracks from the queue

### Daily Playlists Feature

- **Today's Mix**: `/todaysmix [category] [shuffle]` - Access curated daily playlists
  - 🆕 **New Mix**: Latest trending songs and new releases
  - 😌 **Chill**: Relaxing music for studying or unwinding
  - ⚡ **Energetic**: High-energy music for workouts and motivation
- **Auto-refresh**: Playlists automatically refresh weekly
- **Shuffle Option**: Randomize playlist order before playing

### Interactive Controls

- **Button Components**: Skip, previous, pause/resume, stop, volume controls
- **Queue Navigation**: Previous/next page buttons for large queues
- **Category Selection**: Interactive buttons for playlist categories

## Architecture

### Core Components

#### MusicManager (`src/music/MusicManager.ts`)

- Central manager for all music operations
- Handles Lavalink connection and node management
- Manages guild-specific music queues
- Integrates with DailyPlaylistManager

#### MusicQueue (`src/music/MusicQueue.ts`)

- Guild-specific queue management
- Handles playback state (playing, paused, stopped)
- Manages track history and loop modes
- Auto-disconnect when voice channel is empty

#### Track (`src/music/Track.ts`)

- Represents individual music tracks
- Handles metadata (title, author, duration, thumbnail)
- Supports both regular tracks and live streams

#### DailyPlaylistManager (`src/music/DailyPlaylistManager.ts`)

- Manages curated daily playlists
- Auto-refreshes playlists weekly
- Handles multiple playlist categories
- Removes duplicates and shuffles tracks

### Commands Structure

```
src/commands/music/
├── play.ts          # Main play command
├── skip.ts          # Skip current track
├── pause.ts         # Pause/resume playback
├── stop.ts          # Stop and disconnect
├── volume.ts        # Volume control
├── queue.ts         # Display queue
├── nowplaying.ts    # Current track info
├── clear.ts         # Clear queue
└── todaysmix.ts     # Daily playlists
```

### Interactive Components

```
src/components/button/
├── music-skip.ts           # Skip button
├── music-previous.ts       # Previous button
├── music-pause.ts          # Pause/resume button
├── music-stop.ts           # Stop button
├── music-volume.ts         # Volume up/down buttons
├── music-shuffle.ts        # Shuffle queue button
├── queue-clear.ts          # Clear queue button
└── todaysmix-category.ts   # Playlist category buttons
```

## Setup Requirements

### Dependencies

- `@discordjs/voice` - Discord voice connections
- `shoukaku` - Lavalink client for audio processing
- `youtube-dl-exec` - YouTube content extraction
- `spotify-web-api-node` - Spotify API integration
- `ytdl-core` - YouTube audio streaming

### Environment Variables

```env
LAVALINK_URL=localhost:2333
LAVALINK_PASSWORD=youshallnotpass
```

### Lavalink Server Setup

The music system requires a running Lavalink server. We've included setup scripts for easy installation:

#### Quick Setup

```bash
# Navigate to lavalink directory
cd lavalink

# Run setup script (downloads Lavalink and configures)
./setup.sh

# Start Lavalink server
./start.sh
```

#### Manual Setup

1. Download Lavalink.jar from [GitHub releases](https://github.com/lavalink-devs/Lavalink/releases)
2. Use the provided `application.yml` configuration file
3. Start Lavalink server: `java -jar Lavalink.jar`

#### Requirements

- Java 17 or higher
- At least 1GB RAM allocated to Lavalink

## Usage Examples

### Basic Playback

```
/play Never Gonna Give You Up
/play https://www.youtube.com/watch?v=dQw4w9WgXcQ
/play https://open.spotify.com/track/4PTG3Z6ehGkBFwjybzWkR8
```

### Queue Management

```
/queue           # Show current queue
/queue page:2    # Show page 2 of queue
/nowplaying      # Current track details
/clear           # Clear entire queue
```

### Daily Playlists

```
/todaysmix                    # Show category selection
/todaysmix category:new_mix   # Play New Mix playlist
/todaysmix category:chill shuffle:true  # Play shuffled Chill playlist
```

### Volume Control

```
/volume          # Check current volume
/volume level:75 # Set volume to 75%
```

## Permissions Required

### Bot Permissions

- `Connect` - Join voice channels
- `Speak` - Play audio in voice channels
- `Use Voice Activity` - Transmit audio

### User Requirements

- Must be in a voice channel to use music commands
- Must be in the same voice channel as the bot for control commands

## Auto-Cleanup Features

### Voice Channel Management

- Auto-disconnect after 5 minutes of inactivity
- Auto-disconnect when all users leave the voice channel
- Proper cleanup when bot is moved or disconnected

### Queue Management

- Automatic track progression
- Previous track history (up to 10 tracks)
- Loop modes: Off, Track, Queue

## Error Handling

The system includes comprehensive error handling for:

- Network connectivity issues
- Invalid URLs or search queries
- Permission errors
- Lavalink connection problems
- Voice channel access issues

## Performance Considerations

- Uses Lavalink for efficient audio processing
- Implements pagination for large queues
- Caches daily playlists to reduce API calls
- Automatic cleanup prevents memory leaks

## Future Enhancements

Potential improvements for the music system:

- Spotify playlist import
- YouTube playlist support
- User-created playlists
- Music recommendations
- Lyrics display
- Audio filters and effects
- Cross-server playlist sharing
