name: Build and Publish Dist Branch

on:
  push:
    branches:
      - main

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout main branch
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Configure git user
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

      - name: Prepare dist branch content
        run: |
          mkdir /tmp/dist-branch

          # dist klasörünü ve package.json'u kopyala
          cp -r dist /tmp/dist-branch/
          cp package.json /tmp/dist-branch/
          cp package-lock.json /tmp/dist-branch/ || true

      - name: Create and push dist-build branch
        run: |
          git checkout --orphan dist-build
          git reset --hard

          # Temizle
          rm -rf *

          # Dist branch içeriğini kopyala
          cp -r /tmp/dist-branch/* ./
          cp -r /tmp/dist-branch/.[^.]* . 2>/dev/null || true

          git add .
          git commit -m "Update dist build [skip ci]"
          git push --force origin dist-build
