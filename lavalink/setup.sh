#!/bin/bash

# Lavalink Setup Script
echo "🎵 Setting up Lavalink server..."

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo "❌ Java is not installed. Please install Java 17 or higher."
    echo "   You can install it using:"
    echo "   - macOS: brew install openjdk@17"
    echo "   - Ubuntu: sudo apt install openjdk-17-jdk"
    echo "   - Windows: Download from https://adoptium.net/"
    exit 1
fi

# Check Java version
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo "❌ Java 17 or higher is required. Current version: $JAVA_VERSION"
    exit 1
fi

echo "✅ Java version check passed"

# Create logs directory
mkdir -p logs

# Download Lavalink if not exists
LAVALINK_VERSION="4.0.7"
LAVALINK_JAR="Lavalink.jar"

if [ ! -f "$LAVALINK_JAR" ]; then
    echo "📥 Downloading Lavalink v$LAVALINK_VERSION..."
    curl -L "https://github.com/lavalink-devs/Lavalink/releases/download/$LAVALINK_VERSION/Lavalink.jar" -o "$LAVALINK_JAR"
    
    if [ $? -eq 0 ]; then
        echo "✅ Lavalink downloaded successfully"
    else
        echo "❌ Failed to download Lavalink"
        exit 1
    fi
else
    echo "✅ Lavalink.jar already exists"
fi

echo "🎉 Setup complete!"
echo ""
echo "To start Lavalink:"
echo "  ./start.sh"
echo ""
echo "Or manually:"
echo "  java -jar Lavalink.jar"
