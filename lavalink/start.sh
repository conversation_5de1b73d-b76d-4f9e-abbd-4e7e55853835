#!/bin/bash

# Lavalink Start Script
echo "🎵 Starting Lavalink server..."

# Check if Lavalink.jar exists
if [ ! -f "Lavalink.jar" ]; then
    echo "❌ Lavalink.jar not found. Run ./setup.sh first."
    exit 1
fi

# Check if application.yml exists
if [ ! -f "application.yml" ]; then
    echo "❌ application.yml not found."
    exit 1
fi

echo "🚀 Starting Lavalink on port 2333..."
echo "📝 Logs will be saved to ./logs/"
echo "🔑 Password: youshallnotpass"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start Lavalink with proper memory allocation
java -Xmx1G -jar Lavalink.jar
